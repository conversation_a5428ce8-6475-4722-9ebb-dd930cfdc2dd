import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/todo.dart';
import '../models/category.dart' as cat;
import '../services/api_service.dart';
import '../services/category_service.dart';
import '../services/sync_service.dart';
import '../services/error_message_service.dart';
import 'auth_provider.dart';

class TodoProvider with ChangeNotifier {
  static const String _todosKey = 'todos';
  static const String _categoriesKey = 'categories';
  late SharedPreferences _prefs;
  List<Todo> _todos = [];
  List<cat.Category> _categories = [];
  cat.Category? _selectedCategory;
  String _searchQuery = '';
  bool _isListView = true;
  final ApiService _apiService;
  AuthProvider _authProvider;
  final CategoryService _categoryService;
  final SyncService _syncService;
  bool _disposed = false;
  bool _isSyncing = false;

  List<Todo> get todos => _todos;
  List<cat.Category> get categories => _categories;
  cat.Category? get selectedCategory => _selectedCategory;
  String get searchQuery => _searchQuery;
  bool get isListView => _isListView;
  bool get isSyncing => _isSyncing;
  bool get isAuthenticated => _authProvider.isAuthenticated;

  List<Todo> get filteredTodos {
    List<Todo> filtered = List.from(_todos);

    if (_selectedCategory != null) {
      filtered = filtered
          .where((todo) => todo.category == _selectedCategory!.name)
          .toList();
    }

    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered
          .where((todo) =>
              todo.text.toLowerCase().contains(query) ||
              todo.category.toLowerCase().contains(query))
          .toList();
    }

    filtered.sort((a, b) => a.displayOrder.compareTo(b.displayOrder));
    return filtered;
  }

  TodoProvider(this._authProvider, this._categoryService, this._syncService,
      {ApiService? apiService})
      : _apiService =
            apiService ?? ApiService(SharedPreferences.getInstance()) {
    _loadData();

    // Listen for sync status changes
    _syncService.addListener(_onSyncStatusChanged);
  }

  void _onSyncStatusChanged() {
    if (_syncService.isSyncing != _isSyncing) {
      _isSyncing = _syncService.isSyncing;
      notifyListeners();
    }

    // If sync completed, reload data to get the latest from server
    if (!_syncService.isSyncing && _syncService.pendingOperationsCount == 0) {
      _loadData();
    }
  }

  bool get mounted => !_disposed;

  @override
  void dispose() {
    _disposed = true;
    _syncService.removeListener(_onSyncStatusChanged);
    super.dispose();
  }

  void updateAuthProvider(AuthProvider newAuthProvider) {
    if (!mounted) return;

    if (!identical(_authProvider, newAuthProvider)) {
      _authProvider = newAuthProvider;
      _loadData();
    }
  }

  Future<void> _loadData() async {
    if (!mounted) return;

    _prefs = await SharedPreferences.getInstance();
    _isListView = _prefs.getBool('isListView') ?? true;

    // Force a connectivity check
    await _syncService.connectivityService.checkConnectivity();
    debugPrint(
        'Connectivity status: ${_syncService.connectivityService.isConnected ? 'ONLINE' : 'OFFLINE'}');

    // Reset categories in the category service
    _categoryService.updateCategories(_categories);

    if (_authProvider.isAuthenticated) {
      try {
        final response = await _apiService.loadTodos();
        debugPrint(
            'API Response: ${response.toString().substring(0, min(100, response.toString().length))}...');

        if (response['categories'] != null) {
          try {
            _categories = (response['categories'] as List)
                .map((c) {
                  if (c is Map<String, dynamic>) {
                    return cat.Category.fromJson(c);
                  } else {
                    debugPrint('Invalid category format: $c');
                    return cat.Category(
                        id: '', name: 'Uncategorized', color: '#000000');
                  }
                })
                .where((c) => c.name.isNotEmpty)
                .toList();
          } catch (e) {
            debugPrint('Error parsing categories: $e');
            _categories = [
              cat.Category(id: '', name: 'Uncategorized', color: '#000000')
            ];
          }
        } else {
          _categories = [
            cat.Category(id: '', name: 'Uncategorized', color: '#000000')
          ];
        }

        if (response['tasks'] != null) {
          try {
            _todos = (response['tasks'] as List)
                .map((t) {
                  if (t is Map<String, dynamic>) {
                    return Todo.fromJson(t, categories: _categories);
                  } else {
                    debugPrint('Invalid task format: $t');
                    return null;
                  }
                })
                .whereType<Todo>() // Filter out null values
                .toList();
          } catch (e) {
            debugPrint('Error parsing tasks: $e');
            _todos = [];
          }
        }
      } catch (e) {
        // Log the error but don't rethrow to prevent app crash
        debugPrint('Error loading todos from API: $e');
        // Fallback to local storage if API fails
        final todosJson = _prefs.getString(_todosKey);
        if (todosJson != null) {
          try {
            final List<dynamic> decoded = jsonDecode(todosJson);
            _todos = decoded.map((json) => Todo.fromJson(json)).toList();
          } catch (e) {
            debugPrint('Error parsing local todos: $e');
            _todos = [];
          }
        }
        // Fallback: try to load categories as names
        final catList = _prefs.getStringList(_categoriesKey) ?? [];
        _categories = catList
            .map((name) => cat.Category(id: '', name: name, color: '#000000'))
            .toList();
      }
    } else {
      // Load from local storage when not authenticated
      final todosJson = _prefs.getString(_todosKey);
      if (todosJson != null) {
        final List<dynamic> decoded = jsonDecode(todosJson);
        _todos = decoded.map((json) => Todo.fromJson(json)).toList();
      }
      final catList = _prefs.getStringList(_categoriesKey) ?? [];
      _categories = catList
          .map((name) => cat.Category(id: '', name: name, color: '#000000'))
          .toList();
    }

    // Ensure that 'Uncategorized' is always in the categories list if any tasks use it
    if (_todos.any((todo) => todo.category == 'Uncategorized') &&
        !_categories.any((c) => c.name == 'Uncategorized')) {
      _categories
          .add(cat.Category(id: '', name: 'Uncategorized', color: '#000000'));
    }

    // Ensure all todo categories exist in the categories list
    for (final todo in _todos) {
      if (!_categories.any((c) => c.name == todo.category)) {
        _categories
            .add(cat.Category(id: '', name: todo.category, color: '#000000'));
      }
    }

    if (mounted) notifyListeners();
  }

  Future<void> _saveData() async {
    if (_authProvider.isAuthenticated) {
      // Data is already saved to the server through individual operations
      return;
    }
    // Save to local storage when not authenticated
    final todosJson = jsonEncode(_todos.map((todo) => todo.toJson()).toList());
    await _prefs.setString(_todosKey, todosJson);
    await _prefs.setStringList(
        _categoriesKey, _categories.map((c) => c.name).toList());
    await _prefs.setBool('isListView', _isListView);
  }

  Future<void> addTodo(Todo todo) async {
    // Validate required fields
    if (todo.text.trim().isEmpty) {
      debugPrint('Error: Cannot add todo with empty text');
      throw Exception('Text is required');
    }

    if (todo.category.trim().isEmpty) {
      debugPrint('Error: Cannot add todo with empty category');
      throw Exception('Category is required');
    }

    // Set display order for the new todo
    todo.displayOrder = _todos.length;

    if (_authProvider.isAuthenticated) {
      try {
        // Ensure the category exists and get a valid category ID
        int categoryId;

        // Check if category exists in our local list
        final existingCategory = _categories.firstWhere(
          (c) => c.name == todo.category,
          orElse: () => cat.Category(id: '', name: '', color: '#000000'),
        );

        if (existingCategory.name.isEmpty || existingCategory.id.isEmpty) {
          // Category doesn't exist or has no valid ID, create it first
          debugPrint('Creating new category: ${todo.category}');

          if (_syncService.connectivityService.isConnected) {
            // Online mode - create category on server first
            try {
              final data = await _apiService.addCategory(todo.category);
              final newCategory = cat.Category.fromJson(data);
              _categories.add(newCategory);
              _categoryService.updateCategories(_categories);
              categoryId = int.parse(newCategory.id);
              debugPrint('Created category online with ID: $categoryId');
            } catch (e) {
              debugPrint('Failed to create category online: $e');
              throw Exception(
                  'Failed to create category "${todo.category}". Please try again.');
            }
          } else {
            // Offline mode - we can't create a valid category ID, so queue for later
            final newCategory =
                cat.Category(id: '', name: todo.category, color: '#000000');
            _categories.add(newCategory);
            _categoryService.updateCategories(_categories);

            // Add todo locally and queue both category and todo creation
            _todos.add(todo);
            await _saveData();

            // Queue category creation first
            await _syncService.addPendingOperation(
              PendingOperationType.addCategory,
              {'name': todo.category},
            );

            // Then queue todo creation (will be processed after category is created)
            await _syncService.addPendingOperation(
              PendingOperationType.addTodo,
              {
                'text': todo.text.trim(),
                'categoryName': todo
                    .category, // Use category name instead of ID for offline
                'priority': todo.priority.toLowerCase(),
                'dueDate': todo.dueDate?.toIso8601String(),
                'subtasks': todo.subtasks.map((s) => s.toJson()).toList(),
              },
            );

            notifyListeners();
            return; // Exit early for offline mode
          }
        } else {
          // Category exists and has a valid ID
          try {
            categoryId = int.parse(existingCategory.id);
            debugPrint('Using existing category ID: $categoryId');
          } catch (e) {
            debugPrint('Failed to parse category ID: ${existingCategory.id}');
            throw Exception('Invalid category ID. Please try again.');
          }
        }

        // For new todos, we can include subtasks in the initial creation
        final subtasksJson = todo.subtasks.map((s) => s.toJson()).toList();

        if (_syncService.connectivityService.isConnected) {
          // Online mode - add directly to server
          // Validate priority
          String validPriority = todo.priority.toLowerCase();
          if (!['low', 'medium', 'high'].contains(validPriority)) {
            validPriority = 'medium';
          }

          final data = await _apiService.addTodo(
            text: todo.text.trim(),
            categoryId: categoryId,
            priority: validPriority,
            dueDate: todo.dueDate?.toIso8601String(),
            subtasks: subtasksJson,
          );

          // Use the server response to create the todo
          try {
            final newTodo = Todo.fromJson(data,
                categories: _categories as List<cat.Category>?);
            _todos.add(newTodo);
            await _saveData(); // Save to local storage
            debugPrint('Todo added successfully: ${newTodo.text}');
          } catch (parseError) {
            debugPrint('Error parsing server response: $parseError');
            debugPrint('Server response data: $data');
            // Fallback: create a new todo with the server ID
            final fallbackTodo = Todo(
              id: data['id']?.toString() ?? todo.id,
              text: todo.text,
              completed: todo.completed,
              category: todo.category,
              priority: todo.priority,
              dueDate: todo.dueDate,
              subtasks: todo.subtasks,
              displayOrder: todo.displayOrder,
            );
            _todos.add(fallbackTodo);
            await _saveData(); // Save to local storage
            debugPrint('Added fallback todo: ${fallbackTodo.text}');
          }
        } else {
          // Offline mode - add to local storage and queue for sync
          _todos.add(todo);
          await _saveData();

          // Add to sync queue
          // Validate priority
          String validPriority = todo.priority.toLowerCase();
          if (!['low', 'medium', 'high'].contains(validPriority)) {
            validPriority = 'medium';
          }

          await _syncService.addPendingOperation(
            PendingOperationType.addTodo,
            {
              'text': todo.text.trim(),
              'categoryId': categoryId,
              'priority': validPriority,
              'dueDate': todo.dueDate?.toIso8601String(),
              'subtasks': subtasksJson,
            },
          );
        }
      } catch (e) {
        debugPrint('Error adding todo: $e');
        // Show user-friendly error message
        final errorMsg = ErrorMessageService.getUserFriendlyMessage(e);
        debugPrint('User-friendly error: $errorMsg');

        // Fallback to local storage if API fails
        _todos.add(todo);
        await _saveData();
      }
    } else {
      // Not authenticated - just save locally
      _todos.add(todo);
      await _saveData();
    }
    notifyListeners();
  }

  Future<void> updateTodo(Todo todo) async {
    // Ensure the category exists in categories list
    if (!_categories.any((c) => c.name == todo.category)) {
      final newCategory =
          cat.Category(id: '', name: todo.category, color: '#000000');
      _categories.add(newCategory);
      _categoryService.updateCategories(_categories);
    }

    final index = _todos.indexWhere((t) => t.id == todo.id);
    if (index == -1) return; // Todo not found

    if (_authProvider.isAuthenticated) {
      try {
        final categoryId =
            _categoryService.getCategoryIndexByName(todo.category);

        if (_syncService.connectivityService.isConnected) {
          // Online mode - update directly on server
          // Ensure categoryId is valid (not negative)
          final validCategoryId = categoryId < 0 ? 0 : categoryId;

          // Make API call to update the todo properties
          await _apiService.updateTodo(
            id: int.parse(todo.id),
            text: todo.text,
            categoryId: validCategoryId,
            priority: todo.priority,
            dueDate: todo.dueDate?.toIso8601String(),
            subtasks: null, // Subtasks are updated separately
          );

          // Update the local todo
          _todos[index] = todo;
          debugPrint('Todo updated online: ${todo.text}');
        } else {
          // Offline mode - update locally and queue for sync
          _todos[index] = todo;
          await _saveData();

          // Add to sync queue
          // Ensure categoryId is valid (not negative)
          final validCategoryId = categoryId < 0 ? 0 : categoryId;

          await _syncService.addPendingOperation(
            PendingOperationType.updateTodo,
            {
              'id': int.parse(todo.id),
              'text': todo.text,
              'categoryId': validCategoryId,
              'priority': todo.priority,
              'dueDate': todo.dueDate?.toIso8601String(),
              'subtasks': todo.subtasks.map((s) => s.toJson()).toList(),
            },
          );
          debugPrint('Todo updated offline (queued for sync): ${todo.text}');
        }
      } catch (e) {
        debugPrint('Error updating todo: $e');
        // Show user-friendly error message
        final errorMsg = ErrorMessageService.getUserFriendlyMessage(e);
        debugPrint('User-friendly error: $errorMsg');

        // Fallback to local storage if API fails
        _todos[index] = todo;
        await _saveData();
      }
    } else {
      // Not authenticated - just save locally
      _todos[index] = todo;
      await _saveData();
    }
    notifyListeners();
  }

  Future<void> deleteTodo(String id) async {
    if (_authProvider.isAuthenticated) {
      try {
        if (_syncService.connectivityService.isConnected) {
          // Online mode - delete directly on server
          await _apiService.deleteTodo(int.parse(id));
          _todos.removeWhere((todo) => todo.id == id);
          debugPrint('Todo deleted online: $id');
        } else {
          // Offline mode - delete locally and queue for sync
          _todos.removeWhere((todo) => todo.id == id);
          await _saveData();

          // Add to sync queue
          await _syncService.addPendingOperation(
            PendingOperationType.deleteTodo,
            {'id': int.parse(id)},
          );
          debugPrint('Todo deleted offline (queued for sync): $id');
        }
      } catch (e) {
        debugPrint('Error deleting todo: $e');
        // Show user-friendly error message
        final errorMsg = ErrorMessageService.getUserFriendlyMessage(e);
        debugPrint('User-friendly error: $errorMsg');

        // Fallback to local storage if API fails
        _todos.removeWhere((todo) => todo.id == id);
        await _saveData();
      }
    } else {
      // Not authenticated - just delete locally
      _todos.removeWhere((todo) => todo.id == id);
      await _saveData();
    }
    notifyListeners();
  }

  Future<void> toggleTodoComplete(String id) async {
    final index = _todos.indexWhere((todo) => todo.id == id);
    if (index == -1) return; // Todo not found

    // Create updated todo with toggled completion status
    final updatedTodo =
        _todos[index].copyWith(completed: !_todos[index].completed);

    if (_authProvider.isAuthenticated) {
      try {
        if (_syncService.connectivityService.isConnected) {
          // Online mode - update directly on server
          await _apiService.toggleTodoComplete(int.parse(id));
          _todos[index] = updatedTodo;
          debugPrint('Todo completion toggled online: $id');
        } else {
          // Offline mode - update locally and queue for sync
          _todos[index] = updatedTodo;
          await _saveData();

          // Add to sync queue
          await _syncService.addPendingOperation(
            PendingOperationType.toggleTodoComplete,
            {'id': int.parse(id)},
          );
          debugPrint('Todo completion toggled offline (queued for sync): $id');
        }
      } catch (e) {
        debugPrint('Error toggling todo completion: $e');
        // Show user-friendly error message
        final errorMsg = ErrorMessageService.getUserFriendlyMessage(e);
        debugPrint('User-friendly error: $errorMsg');

        // Fallback to local storage if API fails
        _todos[index] = updatedTodo;
        await _saveData();
      }
    } else {
      // Not authenticated - just update locally
      _todos[index] = updatedTodo;
      await _saveData();
    }
    notifyListeners();
  }

  Future<void> reorderTodos(int oldIndex, int newIndex) async {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    final Todo todo = _todos.removeAt(oldIndex);
    _todos.insert(newIndex, todo);

    // Update displayOrder for all todos after reordering
    for (var i = 0; i < _todos.length; i++) {
      _todos[i] = _todos[i].copyWith(displayOrder: i);
    }

    if (_authProvider.isAuthenticated) {
      try {
        final order = _todos
            .map((t) => {
                  'id': t.id,
                  'display_order': t.displayOrder,
                })
            .toList();
        await _apiService.reorderTodos(order);
      } catch (e) {
        await _saveData();
      }
    } else {
      await _saveData();
    }
    notifyListeners();
  }

  Future<void> addCategory(String category) async {
    // Skip if category already exists
    if (_categories.any((c) => c.name == category)) {
      return;
    }

    if (_authProvider.isAuthenticated) {
      try {
        if (_syncService.connectivityService.isConnected) {
          // Online mode - add directly to server
          final data = await _apiService.addCategory(category);
          final newCategory = cat.Category.fromJson(data);
          _categories.add(newCategory);
          _categoryService.updateCategories(_categories);
          debugPrint('Category added online: $category');
        } else {
          // Offline mode - add locally and queue for sync
          final newCategory =
              cat.Category(id: '', name: category, color: '#000000');
          _categories.add(newCategory);
          _categoryService.updateCategories(_categories);
          await _saveData();

          // Add to sync queue
          await _syncService.addPendingOperation(
            PendingOperationType.addCategory,
            {'name': category},
          );
          debugPrint('Category added offline (queued for sync): $category');
        }
      } catch (e) {
        debugPrint('Error adding category: $e');
        // Show user-friendly error message
        final errorMsg = ErrorMessageService.getUserFriendlyMessage(e);
        debugPrint('User-friendly error: $errorMsg');

        // Fallback to local storage if API fails
        final newCategory =
            cat.Category(id: '', name: category, color: '#000000');
        _categories.add(newCategory);
        _categoryService.updateCategories(_categories);
        await _saveData();
      }
    } else {
      // Not authenticated - just add locally
      final newCategory =
          cat.Category(id: '', name: category, color: '#000000');
      _categories.add(newCategory);
      _categoryService.updateCategories(_categories);
      await _saveData();
    }
    notifyListeners();
  }

  Future<void> deleteCategory(String category) async {
    final categoryIndex = _categories.indexWhere((c) => c.name == category);
    if (categoryIndex == -1) return; // Category not found

    final categoryToDelete = _categories[categoryIndex];

    if (_authProvider.isAuthenticated) {
      try {
        if (_syncService.connectivityService.isConnected) {
          // Online mode - delete directly from server
          // Use the actual category ID, not the array index
          int categoryId;
          try {
            categoryId = int.parse(categoryToDelete.id);
          } catch (e) {
            debugPrint(
                'Invalid category ID: ${categoryToDelete.id}, cannot delete online');
            throw Exception('Invalid category ID. Please try again.');
          }

          await _apiService.deleteCategory(categoryId);
          _categories.removeAt(categoryIndex);
          _categoryService.updateCategories(_categories);

          if (_selectedCategory?.name == category) {
            _selectedCategory = null;
          }
          debugPrint('Category deleted online: $category (ID: $categoryId)');
        } else {
          // Offline mode - delete locally and queue for sync
          _categories.removeAt(categoryIndex);
          _categoryService.updateCategories(_categories);

          if (_selectedCategory?.name == category) {
            _selectedCategory = null;
          }
          await _saveData();

          // Add to sync queue - use the actual category ID, not the array index
          int categoryId;
          try {
            categoryId = int.parse(categoryToDelete.id);
          } catch (e) {
            debugPrint(
                'Invalid category ID for offline sync: ${categoryToDelete.id}');
            // For offline mode, we'll store the category name and resolve the ID later during sync
            await _syncService.addPendingOperation(
              PendingOperationType.deleteCategory,
              {
                'categoryName': category
              }, // Store the name to find the category later
            );
            debugPrint(
                'Category deleted offline (queued for sync with name): $category');
            return;
          }

          await _syncService.addPendingOperation(
            PendingOperationType.deleteCategory,
            {'categoryId': categoryId},
          );
          debugPrint(
              'Category deleted offline (queued for sync): $category (ID: $categoryId)');
        }
      } catch (e) {
        debugPrint('Error deleting category: $e');
        // Show user-friendly error message
        final errorMsg = ErrorMessageService.getUserFriendlyMessage(e);
        debugPrint('User-friendly error: $errorMsg');

        // Fallback to local storage if API fails
        _categories.removeWhere((c) => c.name == category);
        _categoryService.updateCategories(_categories);

        if (_selectedCategory?.name == category) {
          _selectedCategory = null;
        }
        await _saveData();
      }
    } else {
      // Not authenticated - just delete locally
      _categories.removeWhere((c) => c.name == category);
      _categoryService.updateCategories(_categories);

      if (_selectedCategory?.name == category) {
        _selectedCategory = null;
      }
      await _saveData();
    }
    notifyListeners();
  }

  Future<void> updateCategory(String oldCategory, String newCategory) async {
    final categoryIndex = _categories.indexWhere((c) => c.name == oldCategory);
    if (categoryIndex == -1) return; // Category not found

    final category = _categories[categoryIndex];

    if (_authProvider.isAuthenticated) {
      try {
        if (_syncService.connectivityService.isConnected) {
          // Online mode - update directly on server
          // Use the actual category ID, not the array index
          int categoryId;
          try {
            categoryId = int.parse(category.id);
          } catch (e) {
            debugPrint(
                'Invalid category ID: ${category.id}, cannot update online');
            throw Exception('Invalid category ID. Please try again.');
          }

          await _apiService.updateCategory(categoryId, newCategory);

          // Update local category
          _categories[categoryIndex] =
              _categories[categoryIndex].copyWith(name: newCategory);
          _categoryService.updateCategories(_categories);

          // Update todos with this category
          for (var i = 0; i < _todos.length; i++) {
            if (_todos[i].category == oldCategory) {
              _todos[i] = _todos[i].copyWith(category: newCategory);
              // We don't need to call updateTodo here as we're just updating the local reference
            }
          }

          if (_selectedCategory?.name == oldCategory) {
            _selectedCategory = _categories[categoryIndex];
          }

          debugPrint(
              'Category updated online: $oldCategory -> $newCategory (ID: $categoryId)');
        } else {
          // Offline mode - update locally and queue for sync
          _categories[categoryIndex] =
              _categories[categoryIndex].copyWith(name: newCategory);
          _categoryService.updateCategories(_categories);

          // Update todos with this category
          for (var i = 0; i < _todos.length; i++) {
            if (_todos[i].category == oldCategory) {
              _todos[i] = _todos[i].copyWith(category: newCategory);
            }
          }

          if (_selectedCategory?.name == oldCategory) {
            _selectedCategory = _categories[categoryIndex];
          }

          await _saveData();

          // Add to sync queue - use the actual category ID, not the array index
          int categoryId;
          try {
            categoryId = int.parse(category.id);
          } catch (e) {
            debugPrint('Invalid category ID for offline sync: ${category.id}');
            // For offline mode, we'll store the category name and resolve the ID later during sync
            await _syncService.addPendingOperation(
              PendingOperationType.updateCategory,
              {
                'categoryName':
                    oldCategory, // Store the old name to find the category later
                'name': newCategory,
              },
            );
            debugPrint(
                'Category updated offline (queued for sync with name): $oldCategory -> $newCategory');
            return;
          }

          await _syncService.addPendingOperation(
            PendingOperationType.updateCategory,
            {
              'categoryId': categoryId,
              'name': newCategory,
            },
          );

          debugPrint(
              'Category updated offline (queued for sync): $oldCategory -> $newCategory (ID: $categoryId)');
        }
      } catch (e) {
        debugPrint('Error updating category: $e');
        debugPrint('Original error: $e');
        // Show user-friendly error message
        final errorMsg = ErrorMessageService.getUserFriendlyMessage(e);
        debugPrint('User-friendly error: $errorMsg');

        // Fallback to local storage if API fails
        _categories[categoryIndex] =
            _categories[categoryIndex].copyWith(name: newCategory);
        _categoryService.updateCategories(_categories);

        // Update todos with this category
        for (var i = 0; i < _todos.length; i++) {
          if (_todos[i].category == oldCategory) {
            _todos[i] = _todos[i].copyWith(category: newCategory);
          }
        }

        if (_selectedCategory?.name == oldCategory) {
          _selectedCategory = _categories[categoryIndex];
        }

        await _saveData();
      }
    } else {
      // Not authenticated - just update locally
      _categories[categoryIndex] =
          _categories[categoryIndex].copyWith(name: newCategory);
      _categoryService.updateCategories(_categories);

      // Update todos with this category
      for (var i = 0; i < _todos.length; i++) {
        if (_todos[i].category == oldCategory) {
          _todos[i] = _todos[i].copyWith(category: newCategory);
        }
      }

      if (_selectedCategory?.name == oldCategory) {
        _selectedCategory = _categories[categoryIndex];
      }

      await _saveData();
    }
    notifyListeners();
  }

  void setSelectedCategory(cat.Category? category) {
    _selectedCategory = category;
    notifyListeners();
  }

  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  void toggleViewMode() {
    _isListView = !_isListView;
    _prefs.setBool('isListView', _isListView);
    notifyListeners();
  }

  Future<void> markAllComplete() async {
    for (var i = 0; i < _todos.length; i++) {
      if (!_todos[i].completed) {
        await updateTodo(_todos[i].copyWith(completed: true));
      }
    }
  }

  Future<void> loadTodosFromApi() async {
    if (!mounted) return;

    try {
      if (!_authProvider.isAuthenticated) {
        throw Exception('User not authenticated');
      }

      // Use existing _loadData method which already handles API loading logic
      await _loadData();
    } catch (e) {
      debugPrint('Error loading todos from API: $e');
      // Don't rethrow to prevent app crashes
    }
  }

  /// Manually trigger a sync of pending operations
  Future<void> syncPendingChanges() async {
    if (!_authProvider.isAuthenticated) return;

    // Force a connectivity check first
    await _syncService.connectivityService.checkConnectivity();

    if (_syncService.connectivityService.isConnected) {
      if (_syncService.pendingOperationsCount > 0) {
        await _syncService.syncPendingOperations();
        // Reload data after sync to ensure we have the latest from server
        await _loadData();
      } else {
        // Even if there are no pending operations, reload data to ensure we have the latest
        await _loadData();
      }
    }
  }

  // Subtask operations
  Future<void> addSubtask(String todoId, Subtask subtask) async {
    final index = _todos.indexWhere((t) => t.id == todoId);
    if (index == -1) return;

    if (_authProvider.isAuthenticated) {
      try {
        if (_syncService.connectivityService.isConnected) {
          // Online mode - add directly to server
          Map<String, dynamic> data;
          try {
            // Try to parse the todoId as an integer
            final parsedId = int.parse(todoId);
            data = await _apiService.addSubtask(
              parsedId,
              subtask.text,
            );
          } catch (e) {
            // If parsing fails, it's likely a UUID, so pass it as is
            debugPrint('Todo ID is not an integer, using as string: $todoId');
            data = await _apiService.addSubtask(
              todoId, // Pass the string ID directly
              subtask.text,
            );
          }

          // Update the subtask with the server-generated ID
          final newSubtask = Subtask(
            id: data['id'].toString(),
            text: data['text'],
            completed: data['completed'] ?? false,
          );

          _todos[index].subtasks.add(newSubtask);
          debugPrint('Subtask added online: ${subtask.text}');
        } else {
          // Offline mode - add locally and queue for sync
          _todos[index].subtasks.add(subtask);
          await _saveData();

          // Add to sync queue
          try {
            // Try to parse the todoId as an integer
            final parsedId = int.parse(todoId);
            await _syncService.addPendingOperation(
              PendingOperationType.addSubtask,
              {
                'todoId': parsedId,
                'text': subtask.text,
              },
            );
          } catch (e) {
            // If parsing fails, it's likely a UUID, so store it as a string
            debugPrint('Todo ID is not an integer, storing as string: $todoId');
            await _syncService.addPendingOperation(
              PendingOperationType.addSubtask,
              {
                'todoId': todoId, // Store the string ID directly
                'text': subtask.text,
              },
            );
          }
          debugPrint(
              'Subtask added offline (queued for sync): ${subtask.text}');
        }
      } catch (e) {
        debugPrint('Error adding subtask: $e');
        // Show user-friendly error message
        final errorMsg = ErrorMessageService.getUserFriendlyMessage(e);
        debugPrint('User-friendly error: $errorMsg');

        // Fallback to local storage
        _todos[index].subtasks.add(subtask);
        await _saveData();
      }
    } else {
      // Not authenticated - just add locally
      _todos[index].subtasks.add(subtask);
      await _saveData();
    }
    notifyListeners();
  }

  Future<void> updateSubtask(String todoId, Subtask subtask) async {
    final todoIndex = _todos.indexWhere((t) => t.id == todoId);
    if (todoIndex == -1) return;

    final subtaskIndex =
        _todos[todoIndex].subtasks.indexWhere((s) => s.id == subtask.id);
    if (subtaskIndex == -1) return;

    if (_authProvider.isAuthenticated) {
      try {
        if (_syncService.connectivityService.isConnected) {
          // Online mode - update directly on server
          try {
            // Try to parse the subtask.id as an integer
            final parsedId = int.parse(subtask.id);
            await _apiService.updateSubtask(
              parsedId,
              subtask.text,
              subtask.completed,
            );
          } catch (e) {
            // If parsing fails, it's likely a UUID, so pass it as is
            debugPrint(
                'Subtask ID is not an integer, using as string: ${subtask.id}');
            await _apiService.updateSubtask(
              subtask.id, // Pass the string ID directly
              subtask.text,
              subtask.completed,
            );
          }

          _todos[todoIndex].subtasks[subtaskIndex] = subtask;
          debugPrint('Subtask updated online: ${subtask.text}');
        } else {
          // Offline mode - update locally and queue for sync
          _todos[todoIndex].subtasks[subtaskIndex] = subtask;
          await _saveData();

          // Add to sync queue
          try {
            // Try to parse the subtask.id as an integer
            final parsedId = int.parse(subtask.id);
            await _syncService.addPendingOperation(
              PendingOperationType.updateSubtask,
              {
                'id': parsedId,
                'text': subtask.text,
                'completed': subtask.completed,
              },
            );
          } catch (e) {
            // If parsing fails, it's likely a UUID, so store it as a string
            debugPrint(
                'Subtask ID is not an integer, storing as string: ${subtask.id}');
            await _syncService.addPendingOperation(
              PendingOperationType.updateSubtask,
              {
                'id': subtask.id, // Store the string ID directly
                'text': subtask.text,
                'completed': subtask.completed,
              },
            );
          }
          debugPrint(
              'Subtask updated offline (queued for sync): ${subtask.text}');
        }
      } catch (e) {
        debugPrint('Error updating subtask: $e');
        // Show user-friendly error message
        final errorMsg = ErrorMessageService.getUserFriendlyMessage(e);
        debugPrint('User-friendly error: $errorMsg');

        // Fallback to local storage
        _todos[todoIndex].subtasks[subtaskIndex] = subtask;
        await _saveData();
      }
    } else {
      // Not authenticated - just update locally
      _todos[todoIndex].subtasks[subtaskIndex] = subtask;
      await _saveData();
    }
    notifyListeners();
  }

  Future<void> deleteSubtask(String todoId, String subtaskId) async {
    final todoIndex = _todos.indexWhere((t) => t.id == todoId);
    if (todoIndex == -1) return;

    final subtaskIndex =
        _todos[todoIndex].subtasks.indexWhere((s) => s.id == subtaskId);
    if (subtaskIndex == -1) return;

    if (_authProvider.isAuthenticated) {
      try {
        if (_syncService.connectivityService.isConnected) {
          // Online mode - delete directly on server
          try {
            // Try to parse the subtaskId as an integer
            final parsedId = int.parse(subtaskId);
            await _apiService.deleteSubtask(parsedId);
          } catch (e) {
            // If parsing fails, it's likely a UUID, so pass it as is
            debugPrint(
                'Subtask ID is not an integer, using as string: $subtaskId');
            await _apiService
                .deleteSubtask(subtaskId); // Pass the string ID directly
          }
          _todos[todoIndex].subtasks.removeAt(subtaskIndex);
          debugPrint('Subtask deleted online: $subtaskId');
        } else {
          // Offline mode - delete locally and queue for sync
          _todos[todoIndex].subtasks.removeAt(subtaskIndex);
          await _saveData();

          // Add to sync queue
          try {
            // Try to parse the subtaskId as an integer
            final parsedId = int.parse(subtaskId);
            await _syncService.addPendingOperation(
              PendingOperationType.deleteSubtask,
              {'id': parsedId},
            );
          } catch (e) {
            // If parsing fails, it's likely a UUID, so store it as a string
            debugPrint(
                'Subtask ID is not an integer, storing as string: $subtaskId');
            await _syncService.addPendingOperation(
              PendingOperationType.deleteSubtask,
              {'id': subtaskId}, // Store the string ID directly
            );
          }
          debugPrint('Subtask deleted offline (queued for sync): $subtaskId');
        }
      } catch (e) {
        debugPrint('Error deleting subtask: $e');
        // Show user-friendly error message
        final errorMsg = ErrorMessageService.getUserFriendlyMessage(e);
        debugPrint('User-friendly error: $errorMsg');

        // Fallback to local storage
        _todos[todoIndex].subtasks.removeAt(subtaskIndex);
        await _saveData();
      }
    } else {
      // Not authenticated - just delete locally
      _todos[todoIndex].subtasks.removeAt(subtaskIndex);
      await _saveData();
    }
    notifyListeners();
  }

  Future<void> toggleSubtaskComplete(String todoId, String subtaskId) async {
    final todoIndex = _todos.indexWhere((t) => t.id == todoId);
    if (todoIndex == -1) return;

    final subtaskIndex =
        _todos[todoIndex].subtasks.indexWhere((s) => s.id == subtaskId);
    if (subtaskIndex == -1) return;

    final subtask = _todos[todoIndex].subtasks[subtaskIndex];
    final updatedSubtask = subtask.copyWith(completed: !subtask.completed);

    if (_authProvider.isAuthenticated) {
      try {
        if (_syncService.connectivityService.isConnected) {
          // Online mode - update directly on server
          try {
            // Try to parse the subtaskId as an integer
            final parsedId = int.parse(subtaskId);
            await _apiService.updateSubtask(
              parsedId,
              updatedSubtask.text,
              updatedSubtask.completed,
            );
          } catch (e) {
            // If parsing fails, it's likely a UUID, so pass it as is
            debugPrint(
                'Subtask ID is not an integer, using as string: $subtaskId');
            await _apiService.updateSubtask(
              subtaskId, // Pass the string ID directly
              updatedSubtask.text,
              updatedSubtask.completed,
            );
          }

          _todos[todoIndex].subtasks[subtaskIndex] = updatedSubtask;
          debugPrint('Subtask completion toggled online: $subtaskId');
        } else {
          // Offline mode - update locally and queue for sync
          _todos[todoIndex].subtasks[subtaskIndex] = updatedSubtask;
          await _saveData();

          // Add to sync queue
          try {
            // Try to parse the subtaskId as an integer
            final parsedId = int.parse(subtaskId);
            await _syncService.addPendingOperation(
              PendingOperationType.updateSubtask,
              {
                'id': parsedId,
                'text': updatedSubtask.text,
                'completed': updatedSubtask.completed,
              },
            );
          } catch (e) {
            // If parsing fails, it's likely a UUID, so store it as a string
            debugPrint(
                'Subtask ID is not an integer, storing as string: $subtaskId');
            await _syncService.addPendingOperation(
              PendingOperationType.updateSubtask,
              {
                'id': subtaskId, // Store the string ID directly
                'text': updatedSubtask.text,
                'completed': updatedSubtask.completed,
              },
            );
          }
          debugPrint(
              'Subtask completion toggled offline (queued for sync): $subtaskId');
        }
      } catch (e) {
        debugPrint('Error toggling subtask: $e');
        // Show user-friendly error message
        final errorMsg = ErrorMessageService.getUserFriendlyMessage(e);
        debugPrint('User-friendly error: $errorMsg');

        // Fallback to local storage
        _todos[todoIndex].subtasks[subtaskIndex] = updatedSubtask;
        await _saveData();
      }
    } else {
      // Not authenticated - just update locally
      _todos[todoIndex].subtasks[subtaskIndex] = updatedSubtask;
      await _saveData();
    }
    notifyListeners();
  }

  // Method to update just the title of a todo
  Future<void> updateTodoTitle(String todoId, String newTitle) async {
    final todoIndex = _todos.indexWhere((t) => t.id == todoId);
    if (todoIndex == -1) return;

    // Store the original todo for reference
    final originalTodo = _todos[todoIndex];

    // Create updated todo with new title but keep all other properties
    final updatedTodo = originalTodo.copyWith(text: newTitle);

    if (_authProvider.isAuthenticated) {
      try {
        // Get the category ID using our CategoryService
        final categoryId =
            _categoryService.getCategoryIndexByName(originalTodo.category);

        // Ensure priority is not empty
        final priority =
            originalTodo.priority.isNotEmpty ? originalTodo.priority : 'medium';

        if (_syncService.connectivityService.isConnected) {
          // Online mode - update directly on server
          debugPrint('Updating todo title online to: $newTitle');

          // Ensure todoId is a valid integer
          int todoIdInt;
          try {
            todoIdInt = int.parse(todoId);
          } catch (e) {
            debugPrint('Error parsing todo ID: $e');
            throw Exception('Invalid todo ID: $todoId');
          }

          // Ensure categoryId is valid (not negative)
          final validCategoryId = categoryId < 0 ? 0 : categoryId;

          final response = await _apiService.updateTodoTitle(
            todoIdInt,
            newTitle,
            priority: priority,
            categoryId: validCategoryId,
            dueDate: originalTodo.dueDate?.toIso8601String(),
          );

          // Update local state with the response from the server
          // but preserve the local subtasks
          final serverTodo = Todo.fromJson(response,
              categories: _categories as List<cat.Category>?);
          final updatedTodoWithSubtasks = serverTodo.copyWith(
            subtasks: originalTodo.subtasks,
          );

          _todos[todoIndex] = updatedTodoWithSubtasks;
          debugPrint('Todo title updated successfully online: $newTitle');
        } else {
          // Offline mode - update locally and queue for sync
          _todos[todoIndex] = updatedTodo;
          await _saveData();

          // Add to sync queue
          // Ensure categoryId is valid (not negative)
          final validCategoryId = categoryId < 0 ? 0 : categoryId;

          await _syncService.addPendingOperation(
            PendingOperationType.updateTodo,
            {
              'id': int.parse(todoId),
              'text': newTitle,
              'categoryId': validCategoryId,
              'priority': priority,
              'dueDate': originalTodo.dueDate?.toIso8601String(),
              'subtasks': originalTodo.subtasks.map((s) => s.toJson()).toList(),
            },
          );
          debugPrint('Todo title updated offline (queued for sync): $newTitle');
        }
      } catch (e) {
        debugPrint('Error updating todo title: $e');
        // Show user-friendly error message
        final errorMsg = ErrorMessageService.getUserFriendlyMessage(e);
        debugPrint('User-friendly error: $errorMsg');

        // Don't update local state to avoid inconsistency
        // This will allow the user to try again
        rethrow; // Rethrow to allow the UI to show an error message
      }
    } else {
      // Not authenticated - just update locally
      _todos[todoIndex] = updatedTodo;
      await _saveData();
    }
    notifyListeners();
  }
}
